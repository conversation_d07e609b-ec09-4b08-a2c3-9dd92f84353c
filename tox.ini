[tox]
requires =
    tox>=4
env_list = py{39,310,311,312,313},docs,pre-commit,check-readme-sync

[gh-actions]
python =
    3.9: py39
    3.10: py310
    3.11: py311
    3.12: py312, docs
    3.13: py313

[testenv]
description = run tests
deps =
    pytest
commands =
    pytest

[testenv:docs]
description = run doc snippets
base_python = 3.12
setenv = PY_IGNORE_IMPORTMISMATCH=1
deps =
    pytest
    pytest-markdown-docs
commands =
    pytest -vv --markdown-docs parmancer examples README.md

[testenv:pre-commit]
skip_install = true
deps = pre-commit
commands = pre-commit run --all-files --show-diff-on-failure

[testenv:check-readme-sync]
description = check README.md sync with module docstring
deps =
    pydoc-markdown
commands = python tools/sync_readme.py --check
