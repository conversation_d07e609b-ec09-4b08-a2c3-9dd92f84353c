# Development Tools

This directory contains development tools for the parmancer project.

## README/Docstring Synchronization Tools

### `sync_readme.py`

Extracts the module docstring from `parmancer/__init__.py` and generates README content using the pydoc-markdown Python API.

**Usage:**
```bash
# Output to stdout
python tools/sync_readme.py

# Output to file
python tools/sync_readme.py README_generated.md

# Via tox
tox -e sync-readme
```

**Features:**
- Uses pydoc-markdown Python API directly (no subprocess calls)
- Extracts only the top-level module docstring (no member documentation)
- Clean markdown output suitable for README files
- Configurable output destination

### `check_readme_sync.py`

Checks if README.md is synchronized with the module docstring from `parmancer/__init__.py`.

**Usage:**
```bash
# Check synchronization (exit code 0 if synced, 1 if not)
python tools/check_readme_sync.py

# Show differences
python tools/check_readme_sync.py --diff

# Check different README file
python tools/check_readme_sync.py --readme docs/README.md

# Via tox
tox -e check-readme-sync
```

**Features:**
- Compares normalized content (handles whitespace differences)
- Provides clear error messages with instructions
- Can show unified diff of differences
- Suitable for CI/CD integration

## Integration

### Tox Environments

Two tox environments are available:

- `sync-readme`: Generates README content from module docstring
- `check-readme-sync`: Validates that README and docstring are synchronized

### CI/CD Integration

The check tool can be integrated into CI pipelines:

```yaml
# GitHub Actions example
- name: Check README sync
  run: tox -e check-readme-sync
```

### Pre-commit Hook

Can be added to `.pre-commit-config.yaml`:

```yaml
- repo: local
  hooks:
  - id: check-readme-sync
    name: Check README/docstring sync
    entry: python tools/check_readme_sync.py
    language: system
    files: (README\.md|parmancer/__init__\.py)
    pass_filenames: false
```

## Dependencies

Both tools require `pydoc-markdown` which is included in the dev dependency group.
