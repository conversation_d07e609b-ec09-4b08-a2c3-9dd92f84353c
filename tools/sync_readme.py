#!/usr/bin/env python3
"""
Development tool to extract the module docstring from parmancer/__init__.py
and generate README content using pydoc-markdown.

This provides a way to keep README.md and package docstrings synchronized
by using the package docstring as the single source of truth.
"""

import sys
from pathlib import Path
from typing import List

from pydoc_markdown import PydocMarkdown
from pydoc_markdown.contrib.loaders.python import PythonLoader
from pydoc_markdown.contrib.processors.filter import FilterProcessor
from pydoc_markdown.contrib.renderers.markdown import MarkdownRenderer


def create_pydoc_config() -> PydocMarkdown:
    """
    Create a PydocMarkdown configuration that extracts only the module docstring
    from parmancer/__init__.py.
    """
    # Python loader to load the parmancer module
    loader = PythonLoader(
        search_path=["."],
        modules=["parmancer"]
    )
    
    # Filter processor to keep only the module docstring, exclude all members
    filter_processor = FilterProcessor(
        expression="not hasattr(obj, 'parent') or obj.parent is None",
        documented_only=False,
        exclude_private=False,
        exclude_special=False,
        do_not_filter_modules=True,
        skip_empty_modules=False
    )
    
    # Markdown renderer with clean output
    renderer = MarkdownRenderer(
        render_module_header=False,
        render_toc=False,
        descriptive_class_title=False,
        add_method_class_prefix=False,
        add_member_class_prefix=False
    )
    
    return PydocMarkdown(
        loaders=[loader],
        processors=[filter_processor],
        renderer=renderer
    )


def extract_module_docstring() -> str:
    """
    Extract the module docstring from parmancer/__init__.py and return it as markdown.
    
    Returns:
        The module docstring formatted as markdown.
    """
    # Create the configuration
    pydoc = create_pydoc_config()
    
    # Load modules
    modules = pydoc.load_modules()
    
    # Process modules (apply filters)
    pydoc.process(modules)
    
    # Render to markdown - we need to capture the output
    # The MarkdownRenderer writes to a file or stdout, so we need to handle this
    import io
    from contextlib import redirect_stdout
    
    # Temporarily redirect stdout to capture the markdown output
    markdown_output = io.StringIO()
    with redirect_stdout(markdown_output):
        pydoc.render(modules)
    
    return markdown_output.getvalue()


def main():
    """Main entry point for the sync_readme tool."""
    try:
        # Extract the module docstring as markdown
        markdown_content = extract_module_docstring()
        
        if not markdown_content.strip():
            print("Warning: No content extracted from module docstring", file=sys.stderr)
            sys.exit(1)
        
        # Write to stdout by default, or to a file if specified
        if len(sys.argv) > 1:
            output_file = Path(sys.argv[1])
            output_file.write_text(markdown_content)
            print(f"Module docstring extracted to: {output_file}")
        else:
            print(markdown_content, end="")
        
    except Exception as e:
        print(f"Error extracting module docstring: {e}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
