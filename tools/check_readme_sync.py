#!/usr/bin/env python3
"""
Development tool to check if README.md is synchronized with the module docstring
from parmancer/__init__.py.

This script can be used in CI/CD pipelines to ensure documentation stays in sync.
"""

import sys
import tempfile
from pathlib import Path
from typing import Optional

from sync_readme import extract_module_docstring


def normalize_content(content: str) -> str:
    """
    Normalize content for comparison by removing trailing whitespace and 
    ensuring consistent line endings.
    """
    lines = content.strip().split('\n')
    return '\n'.join(line.rstrip() for line in lines)


def check_readme_sync(readme_path: Path = Path("README.md")) -> bool:
    """
    Check if README.md is synchronized with the module docstring.
    
    Args:
        readme_path: Path to the README.md file to check
        
    Returns:
        True if synchronized, False otherwise
    """
    if not readme_path.exists():
        print(f"Error: {readme_path} does not exist", file=sys.stderr)
        return False
    
    try:
        # Extract current module docstring
        docstring_content = extract_module_docstring()
        
        # Read current README content
        readme_content = readme_path.read_text()
        
        # Normalize both for comparison
        normalized_docstring = normalize_content(docstring_content)
        normalized_readme = normalize_content(readme_content)
        
        # Compare
        if normalized_docstring == normalized_readme:
            return True
        else:
            print("README.md is not synchronized with module docstring", file=sys.stderr)
            print("\nTo update README.md from docstring, run:", file=sys.stderr)
            print("  python tools/sync_readme.py README.md", file=sys.stderr)
            print("  # or", file=sys.stderr)
            print("  tox -e sync-readme", file=sys.stderr)
            return False
            
    except Exception as e:
        print(f"Error checking README sync: {e}", file=sys.stderr)
        return False


def show_diff(readme_path: Path = Path("README.md")) -> None:
    """
    Show the differences between README.md and the module docstring.
    """
    try:
        import difflib
        
        # Extract current module docstring
        docstring_content = extract_module_docstring()
        
        # Read current README content
        readme_content = readme_path.read_text()
        
        # Generate diff
        diff = difflib.unified_diff(
            readme_content.splitlines(keepends=True),
            docstring_content.splitlines(keepends=True),
            fromfile=str(readme_path),
            tofile="module docstring",
            lineterm=""
        )
        
        print("Differences between README.md and module docstring:")
        print("".join(diff))
        
    except Exception as e:
        print(f"Error generating diff: {e}", file=sys.stderr)


def main():
    """Main entry point for the check_readme_sync tool."""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="Check if README.md is synchronized with module docstring"
    )
    parser.add_argument(
        "--readme", 
        type=Path, 
        default=Path("README.md"),
        help="Path to README.md file (default: README.md)"
    )
    parser.add_argument(
        "--diff",
        action="store_true",
        help="Show differences instead of just checking"
    )
    
    args = parser.parse_args()
    
    if args.diff:
        show_diff(args.readme)
    else:
        is_synced = check_readme_sync(args.readme)
        if is_synced:
            print("README.md is synchronized with module docstring")
            sys.exit(0)
        else:
            sys.exit(1)


if __name__ == "__main__":
    main()
