# README/Docstring Synchronization Example

This example demonstrates how to use `pydoc-markdown` to extract just the top-level module docstring from `parmancer/__init__.py` and generate README content, providing a solution for keeping README.md and package docstrings synchronized.

## Setup

The `pydoc-markdown` tool has been added to the dev dependencies:

```bash
uv add --group dev pydoc-markdown
```

## Configuration

The `pydoc-markdown.yml` configuration file extracts only the module docstring:

```yaml
loaders:
- type: python
  search_path: [.]
  modules: [parmancer]

processors:
- type: filter
  # Keep only the module docstring, exclude all members
  expression: "not hasattr(obj, 'parent') or obj.parent is None"
  documented_only: false
  exclude_private: false
  exclude_special: false
  do_not_filter_modules: true
  skip_empty_modules: false

renderer:
  type: markdown
  render_module_header: false
  render_toc: false
  descriptive_class_title: false
  add_method_class_prefix: false
  add_member_class_prefix: false
```

## Usage

### Command Line

Extract just the module docstring:

```bash
uv run pydoc-markdown
```

### Python Script

The `generate_readme_from_docstring.py` script demonstrates programmatic usage:

```bash
uv run python generate_readme_from_docstring.py
```

## Key Features

1. **Module-only extraction**: The filter expression `"not hasattr(obj, 'parent') or obj.parent is None"` ensures only top-level modules are included, excluding all members (functions, classes, variables).

2. **Clean output**: The renderer configuration removes headers, TOC, and other formatting that would be redundant in a README.

3. **Automation ready**: Can be integrated into build scripts, pre-commit hooks, or CI/CD pipelines.

## Integration Options

### Pre-commit Hook
Add to `.pre-commit-config.yaml`:
```yaml
- repo: local
  hooks:
  - id: sync-readme-docstring
    name: Sync README with docstring
    entry: uv run pydoc-markdown
    language: system
    files: parmancer/__init__.py
    pass_filenames: false
```

### Make Target
Add to `Makefile`:
```makefile
sync-readme:
	uv run pydoc-markdown > README_generated.md
```

### GitHub Action
```yaml
- name: Check README sync
  run: |
    uv run pydoc-markdown > README_from_docstring.md
    diff README.md README_from_docstring.md || exit 1
```

## Benefits

- **Single source of truth**: Keep documentation in the package docstring
- **Automatic sync**: Generate README from docstring programmatically  
- **Version control friendly**: Changes to docstring automatically reflect in generated README
- **CI integration**: Can validate that README matches docstring in automated builds
