#!/usr/bin/env python3
"""
Example script showing how to use pydoc-markdown to extract just the module docstring
from parmancer/__init__.py and generate README content.

This demonstrates one approach to keeping README.md and package docstrings in sync.
"""

import subprocess
import sys
from pathlib import Path


def generate_readme_from_docstring() -> str:
    """
    Use pydoc-markdown to extract just the module docstring from parmancer/__init__.py.
    
    Returns the generated markdown content as a string.
    """
    try:
        # Run pydoc-markdown with our configuration
        result = subprocess.run(
            ["uv", "run", "pydoc-markdown"],
            capture_output=True,
            text=True,
            check=True,
            cwd=Path(__file__).parent
        )
        return result.stdout
    except subprocess.CalledProcessError as e:
        print(f"Error running pydoc-markdown: {e}", file=sys.stderr)
        print(f"stderr: {e.stderr}", file=sys.stderr)
        sys.exit(1)


def main():
    """Generate README content from the module docstring."""
    print("Generating README content from parmancer module docstring...")
    
    # Generate the content
    content = generate_readme_from_docstring()
    
    # Write to a file
    output_file = Path("README_from_docstring.md")
    output_file.write_text(content)
    
    print(f"Generated README content written to: {output_file}")
    print(f"Content length: {len(content)} characters")
    
    # Show first few lines as preview
    lines = content.split('\n')
    print("\nFirst 10 lines of generated content:")
    for i, line in enumerate(lines[:10], 1):
        print(f"{i:2}: {line}")
    
    if len(lines) > 10:
        print(f"... and {len(lines) - 10} more lines")


if __name__ == "__main__":
    main()
